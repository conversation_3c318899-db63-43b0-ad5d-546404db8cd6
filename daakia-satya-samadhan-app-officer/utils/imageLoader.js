import React, { useState } from 'react';
import { View, Image, ActivityIndicator, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';

/**
 * ImageWithLoader - A reusable component that shows a loader while image is loading
 * and handles error states with fallback icons
 * 
 * @param {string} source - Image URI or source object
 * @param {object} style - Style object for the image
 * @param {object} containerStyle - Style object for the container
 * @param {string} loaderColor - Color for the loading indicator (default: Colors.primary)
 * @param {string} loaderSize - Size of the loading indicator ('small' | 'large', default: 'small')
 * @param {string} fallbackIcon - Ionicons name for fallback icon (default: 'image-outline')
 * @param {string} fallbackIconColor - Color for fallback icon (default: Colors.lightText)
 * @param {number} fallbackIconSize - Size for fallback icon (default: 24)
 * @param {function} onLoad - Callback when image loads successfully
 * @param {function} onError - Callback when image fails to load
 * @param {boolean} showSecurityCheck - Whether to check for HTTPS URLs (default: true)
 * @param {object} imageProps - Additional props to pass to the Image component
 */
export const ImageWithLoader = ({
  source,
  style,
  containerStyle,
  loaderColor = Colors.primary,
  loaderSize = 'small',
  fallbackIcon = 'image-outline',
  fallbackIconColor = Colors.lightText,
  fallbackIconSize = 24,
  onLoad,
  onError,
  showSecurityCheck = true,
  ...imageProps
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Convert source to URI string if it's an object
  const imageUri = typeof source === 'string' ? source : source?.uri;
  
  // Security check for HTTPS URLs
  const isSecureUrl = !showSecurityCheck || (imageUri && imageUri.startsWith('https://'));
  
  // Don't render if no source or insecure URL
  if (!imageUri || !isSecureUrl) {
    return (
      <View style={[styles.container, containerStyle, style]}>
        <Ionicons 
          name={fallbackIcon} 
          size={fallbackIconSize} 
          color={fallbackIconColor} 
        />
      </View>
    );
  }

  const handleLoad = () => {
    setIsLoading(false);
    setHasError(false);
    if (onLoad) onLoad();
  };

  const handleError = (error) => {
    setIsLoading(false);
    setHasError(true);
    if (onError) onError(error);
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {/* Show loader while loading */}
      {isLoading && !hasError && (
        <View style={[styles.loaderContainer, style]}>
          <ActivityIndicator size={loaderSize} color={loaderColor} />
        </View>
      )}
      
      {/* Show fallback icon on error */}
      {hasError && (
        <View style={[styles.fallbackContainer, style]}>
          <Ionicons 
            name={fallbackIcon} 
            size={fallbackIconSize} 
            color={fallbackIconColor} 
          />
        </View>
      )}
      
      {/* Show image when loaded successfully */}
      {!hasError && (
        <Image
          source={{ uri: imageUri }}
          style={[style, isLoading && styles.hiddenImage]}
          onLoad={handleLoad}
          onError={handleError}
          {...imageProps}
        />
      )}
    </View>
  );
};

/**
 * Hook for managing image loading states
 * 
 * @param {string} imageUri - The image URI to load
 * @param {boolean} checkSecurity - Whether to check for HTTPS (default: true)
 * @returns {object} - { isLoading, hasError, isSecure, loadImage, resetState }
 */
export const useImageLoader = (imageUri, checkSecurity = true) => {
  const [isLoading, setIsLoading] = useState(false);
  const [hasError, setHasError] = useState(false);
  
  const isSecure = !checkSecurity || (imageUri && imageUri.startsWith('https://'));
  
  const loadImage = () => {
    if (!imageUri || !isSecure) {
      setHasError(true);
      return;
    }
    
    setIsLoading(true);
    setHasError(false);
    
    // Create a new Image object to preload
    const img = new Image();
    
    img.onload = () => {
      setIsLoading(false);
      setHasError(false);
    };
    
    img.onerror = () => {
      setIsLoading(false);
      setHasError(true);
    };
    
    img.src = imageUri;
  };
  
  const resetState = () => {
    setIsLoading(false);
    setHasError(false);
  };
  
  return {
    isLoading,
    hasError,
    isSecure,
    loadImage,
    resetState
  };
};

/**
 * Utility function to transform and validate image URLs
 * 
 * @param {string} url - The image URL to process
 * @param {function} transformFn - Optional transform function (like transformUrl)
 * @param {boolean} requireHttps - Whether to require HTTPS (default: true)
 * @returns {object} - { isValid, transformedUrl, isSecure }
 */
export const processImageUrl = (url, transformFn, requireHttps = true) => {
  if (!url || typeof url !== 'string') {
    return { isValid: false, transformedUrl: null, isSecure: false };
  }
  
  // Apply transformation if provided
  const transformedUrl = transformFn ? transformFn(url) : url;
  
  // Check security
  const isSecure = transformedUrl.startsWith('https://');
  const isValid = !requireHttps || isSecure;
  
  return {
    isValid,
    transformedUrl: isValid ? transformedUrl : null,
    isSecure
  };
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loaderContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    zIndex: 1,
  },
  fallbackContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  hiddenImage: {
    opacity: 0,
  },
});

export default ImageWithLoader;
